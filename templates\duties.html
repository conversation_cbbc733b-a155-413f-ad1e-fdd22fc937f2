{% extends "base.html" %}

{% block title %}كشف الواجبات{% endblock %}

{% block head %}
<meta name="csrf-token" content="{{ csrf_token() }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/receipts.css') }}">
<!-- مكتبة التاريخ الهجري -->
<script src="https://cdn.jsdelivr.net/npm/moment@2.29.4/moment.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/moment-hijri@2.1.2/moment-hijri.min.js"></script>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h3 style="color: var(--text-primary) !important;">
                <i class="fas fa-clipboard-list"></i> كشف الواجبات
            </h3>
            <p class="text-muted">إنشاء وإدارة كشوف الواجبات</p>
        </div>
        <div class="col-md-4 text-right">
            <button type="button" class="btn btn-warning me-2" onclick="clearAllData()">
                <i class="fas fa-trash"></i> مسح البيانات
            </button>
            <button type="button" class="btn btn-success me-2" onclick="saveReceipt()">
                <i class="fas fa-save"></i> حفظ
            </button>
            <button type="button" class="btn btn-success" onclick="exportToExcel()">
                <i class="fas fa-file-excel"></i> تصدير
            </button>
        </div>
    </div>

    <!-- Receipt Form -->
    <div class="card">
        <div class="card-header text-center">
            <h4 class="mb-3" style="color: var(--text-primary) !important;">كشف الواجبات</h4>
            <div class="row">
                <div class="col-md-3">
                    <label>اليوم:</label>
                    <input type="text" id="dayName" class="form-control" readonly style="background-color: #f8f9fa;">
                </div>
                <div class="col-md-3">
                    <label>التاريخ الهجري:</label>
                    <input type="text" id="hijriDate" class="form-control" readonly style="background-color: #f8f9fa;">
                </div>
                <div class="col-md-3">
                    <label>التاريخ الميلادي:</label>
                    <input type="text" id="gregorianDate" class="form-control" readonly style="background-color: #f8f9fa;">
                </div>
                <div class="col-md-3">
                    <label>رقم الكشف:</label>
                    <input type="text" id="receiptNumber" class="form-control" readonly style="background-color: #f8f9fa;">
                </div>
            </div>
        </div>

        <div class="card-body">
            <!-- Table Controls -->
            <div class="table-controls mb-3">
                <div class="row">
                    <div class="col-md-12">
                        <button type="button" class="btn btn-success btn-sm me-2" onclick="addColumn()">
                            <i class="fas fa-plus"></i> إضافة عمود
                        </button>
                        <button type="button" class="btn btn-success btn-sm me-2" onclick="addRow()">
                            <i class="fas fa-plus"></i> إضافة صف
                        </button>
                        <button type="button" class="btn btn-secondary btn-sm me-2" onclick="resetHeaders()">
                            <i class="fas fa-eraser"></i> تفريغ الكشف
                        </button>
                    </div>
                </div>
            </div>

            <!-- Main Receipt Table -->
            <div class="table-responsive">
                <table class="table table-bordered receipts-table" id="dutyTable">
                    <thead id="tableHeader">
                        <!-- Dynamic header will be generated -->
                    </thead>
                    <tbody id="receiptTableBody">
                        <!-- Dynamic rows will be generated -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Patrol Notes Table -->
    <div class="card mt-4">
        <div class="card-header text-center">
            <h5 class="mb-0" style="color: var(--text-primary) !important;">كشف واجبات الدوريات</h5>
        </div>
        <div class="card-body">
            <!-- Patrol Table Controls -->
            <div class="table-controls mb-3">
                <div class="row">
                    <div class="col-md-12">
                        <button type="button" class="btn btn-success btn-sm me-2" onclick="addPatrolColumn()">
                            <i class="fas fa-plus"></i> إضافة عمود
                        </button>
                        <button type="button" class="btn btn-success btn-sm me-2" onclick="addPatrolRow()">
                            <i class="fas fa-plus"></i> إضافة صف
                        </button>
                        <button type="button" class="btn btn-secondary btn-sm me-2" onclick="resetPatrolTable()">
                            <i class="fas fa-eraser"></i> تفريغ الجدول
                        </button>
                    </div>
                </div>
            </div>

            <!-- Patrol Notes Table -->
            <div class="table-responsive">
                <table class="table table-bordered receipts-table" id="patrolTable">
                    <thead id="patrolTableHeader">
                        <!-- Dynamic header will be generated -->
                    </thead>
                    <tbody id="patrolTableBody">
                        <!-- Dynamic rows will be generated -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Shifts Table -->
    <div class="card mt-4">
        <div class="card-header text-center">
            <h5 class="mb-0" style="color: var(--text-primary) !important;">كشف المناوبين</h5>
        </div>
        <div class="card-body">
            <!-- Shifts Table Controls -->
            <div class="table-controls mb-3">
                <div class="row">
                    <div class="col-md-12">
                        <button type="button" class="btn btn-success btn-sm me-2" onclick="addShiftsColumn()">
                            <i class="fas fa-plus"></i> إضافة عمود
                        </button>
                        <button type="button" class="btn btn-success btn-sm me-2" onclick="addShiftsRow()">
                            <i class="fas fa-plus"></i> إضافة صف
                        </button>
                        <button type="button" class="btn btn-secondary btn-sm me-2" onclick="resetShiftsTable()">
                            <i class="fas fa-eraser"></i> تفريغ الجدول
                        </button>
                    </div>
                </div>
            </div>

            <!-- Shifts Table -->
            <div class="table-responsive">
                <table class="table table-bordered receipts-table" id="shiftsTable">
                    <thead id="shiftsTableHeader">
                        <!-- Dynamic header will be generated -->
                    </thead>
                    <tbody id="shiftsTableBody">
                        <!-- Dynamic rows will be generated -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Personnel Search Modal -->
<div class="modal fade" id="personnelSearchModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">🔍 البحث عن الأفراد</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="search-section mb-4">
                    <div class="row">
                        <div class="col-md-8">
                            <label class="form-label">رقم الهوية الوطنية (10 أرقام):</label>
                            <input type="text" class="form-control" id="searchNationalId"
                                   placeholder="أدخل رقم الهوية الوطنية" maxlength="10"
                                   style="color: #000 !important; background-color: #fff !important;"
                                   oninput="searchPersonnelLive(this.value)">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">&nbsp;</label>
                            <button type="button" class="btn btn-primary w-100" onclick="searchPersonnel()">
                                <i class="fas fa-search"></i> بحث
                            </button>
                        </div>
                    </div>
                </div>

                <div class="search-results" id="searchResults">
                    <!-- سيتم ملء النتائج هنا تلقائياً -->
                </div>

                <div class="personnel-details" id="personnelDetails" style="display: none;">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">تفاصيل الفرد</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>الاسم:</strong> <span id="detailName"></span>
                                </div>
                                <div class="col-md-6">
                                    <strong>الرتبة:</strong> <span id="detailRank"></span>
                                </div>
                                <div class="col-md-6">
                                    <strong>الوحدة:</strong> <span id="detailUnit"></span>
                                </div>
                                <div class="col-md-6">
                                    <strong>رقم الهوية:</strong> <span id="detailNationalId"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-success" id="insertPersonnelBtn" onclick="insertPersonnel()" disabled>
                    <i class="fas fa-check"></i> إدراج في الخانة
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Add Location Modal -->
<div class="modal fade" id="addLocationModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة موقع جديد</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="locationForm">
                    <div class="mb-3">
                        <label class="form-label">اسم الموقع *</label>
                        <input type="text" class="form-control" id="locationName" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">نوع الموقع</label>
                        <select class="form-control" id="locationType">
                            <option value="أمني">أمني</option>
                            <option value="إداري">إداري</option>
                            <option value="حراسة">حراسة</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">الوصف</label>
                        <textarea class="form-control" id="locationDescription" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveLocation()">
                    <i class="fas fa-save"></i> حفظ
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Print Styles -->
<style>
/* تنسيق الرؤوس القابلة للتحرير */
.editable-header {
    background: transparent !important;
    border: none !important;
    color: inherit !important;
    font-weight: bold !important;
    text-align: center !important;
    width: 100% !important;
    padding: 4px !important;
    font-size: inherit !important;
}

.editable-header:focus {
    background: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid var(--primary-color) !important;
    outline: none !important;
}

@media print {
    .btn, .modal, .navbar, .sidebar {
        display: none !important;
    }

    .card {
        box-shadow: none !important;
        border: 1px solid #000 !important;
    }

    .table {
        font-size: 10px;
    }

    .table th,
    .table td {
        padding: 4px 2px;
        border: 1px solid #000 !important;
    }

    .card {
        page-break-inside: avoid;
    }

    body {
        background: white !important;
        color: black !important;
    }

    .form-control {
        border: none !important;
        background: transparent !important;
        color: black !important;
    }
}
</style>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/duties-simple.js') }}"></script>
{% endblock %}